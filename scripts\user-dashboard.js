// User Dashboard JavaScript

let map
let vehicleMarkers = []
let userLocation = null
const L = window.L // Declare the L variable

// Initialize user dashboard
document.addEventListener("DOMContentLoaded", () => {
  initializeUserDashboard()
  initializeMap()
  loadUserData()
  startRealTimeUpdates()
  setupNotifications()
})

// Initialize user dashboard
function initializeUserDashboard() {
  console.log("User Dashboard Initialized")

  // Check authentication
  const user = JSON.parse(localStorage.getItem("waamanUser"))
  if (!user || user.role !== "user") {
    window.location.href = "index.html"
    return
  }

  updateUserInfo(user)
  setupEventListeners()
}

// Update user info
function updateUserInfo(user) {
  const userName = document.querySelector(".user-name")
  if (userName) {
    userName.textContent = user.name
  }
}

// Setup event listeners
function setupEventListeners() {
  const notificationBtn = document.getElementById("notificationBtn")
  const notificationDropdown = document.getElementById("notificationDropdown")

  if (notificationBtn && notificationDropdown) {
    notificationBtn.addEventListener("click", (e) => {
      e.stopPropagation()
      notificationDropdown.classList.toggle("hidden")
    })

    document.addEventListener("click", () => {
      notificationDropdown.classList.add("hidden")
    })
  }

  // Complaint form
  const complaintForm = document.getElementById("complaintForm")
  if (complaintForm) {
    complaintForm.addEventListener("submit", handleComplaintSubmission)
  }
}

// Initialize map
function initializeMap() {
  try {
    // Initialize Leaflet map
    if (typeof L !== "undefined") {
      map = L.map("map").setView([40.7128, -74.006], 13)

      // Add tile layer
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "© OpenStreetMap contributors",
      }).addTo(map)

      // Get user location
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            userLocation = [position.coords.latitude, position.coords.longitude]
            map.setView(userLocation, 15)

            // Add user location marker
            L.marker(userLocation).addTo(map).bindPopup("Your Location").openPopup()

            loadVehicleMarkers()
          },
          (error) => {
            console.log("Geolocation error:", error)
            loadVehicleMarkers()
          },
        )
      } else {
        loadVehicleMarkers()
      }
    } else {
      showMapFallback()
    }
  } catch (error) {
    console.log("Map initialization error:", error)
    showMapFallback()
  }
}

// Show map fallback
function showMapFallback() {
  const mapContainer = document.getElementById("map")
  if (mapContainer) {
    mapContainer.innerHTML = `
      <div class="h-full flex items-center justify-center bg-gray-100 rounded-2xl">
        <div class="text-center">
          <i class="fas fa-map-marked-alt text-4xl text-gray-400 mb-4"></i>
          <p class="text-gray-600">Map view unavailable</p>
          <p class="text-sm text-gray-500">Vehicle locations will be shown in list below</p>
        </div>
      </div>
    `
  }
}

// Load vehicle markers on map
function loadVehicleMarkers() {
  if (!map || typeof L === "undefined") return

  // Clear existing markers
  vehicleMarkers.forEach((marker) => map.removeLayer(marker))
  vehicleMarkers = []

  // Add vehicle markers
  window.WaaManApp.vehicles.forEach((vehicle) => {
    const marker = L.marker([vehicle.location.lat, vehicle.location.lng])
      .addTo(map)
      .bindPopup(`
        <div class="p-2">
          <h4 class="font-bold">${vehicle.name}</h4>
          <p class="text-sm text-gray-600">Driver: ${vehicle.driver}</p>
          <p class="text-sm text-gray-600">ETA: ${vehicle.eta}</p>
          <p class="text-sm">
            <span class="px-2 py-1 rounded text-xs ${window.WaaManApp.getStatusColor(vehicle.status)}">
              ${vehicle.status.toUpperCase()}
            </span>
          </p>
        </div>
      `)

    vehicleMarkers.push(marker)
  })
}

// Load user data
function loadUserData() {
  updateQuickStats()
  loadVehicleList()
  loadComplaintsList()
}

// Update quick stats
function updateQuickStats() {
  const nearbyVehicles = window.WaaManApp.vehicles.filter((v) => v.status === "active").length
  const nextCollection = window.WaaManApp.vehicles.find((v) => v.status === "active")?.eta || "N/A"
  const myComplaints = window.WaaManApp.complaints.filter((c) => c.status === "pending").length
  const resolvedComplaints = window.WaaManApp.complaints.filter((c) => c.status === "resolved").length

  const nearbyElement = document.getElementById("nearbyVehicles")
  const nextElement = document.getElementById("nextCollection")
  const complaintsElement = document.getElementById("myComplaints")
  const resolvedElement = document.getElementById("resolvedComplaints")

  if (nearbyElement) nearbyElement.textContent = nearbyVehicles
  if (nextElement) nextElement.textContent = nextCollection
  if (complaintsElement) complaintsElement.textContent = myComplaints
  if (resolvedElement) resolvedElement.textContent = resolvedComplaints
}

// Load vehicle list
function loadVehicleList() {
  const vehicleList = document.getElementById("vehicleList")
  if (!vehicleList) return

  vehicleList.innerHTML = ""

  window.WaaManApp.vehicles.forEach((vehicle) => {
    const vehicleItem = document.createElement("div")
    vehicleItem.className =
      "flex items-center justify-between p-6 border border-gray-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 bg-white/50 backdrop-blur-sm"

    vehicleItem.innerHTML = `
      <div class="flex items-center space-x-4">
        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
          <i class="fas fa-truck text-white text-xl"></i>
        </div>
        <div>
          <h4 class="font-bold text-gray-900 text-lg">${vehicle.name}</h4>
          <p class="text-sm text-gray-600">Driver: ${vehicle.driver}</p>
          <p class="text-sm text-gray-600">Route: ${vehicle.route}</p>
        </div>
      </div>
      <div class="text-right">
        <span class="px-4 py-2 rounded-full text-sm font-semibold ${window.WaaManApp.getStatusColor(vehicle.status)}">
          ${vehicle.status.toUpperCase()}
        </span>
        <p class="text-sm text-gray-600 mt-2">ETA: ${vehicle.eta}</p>
      </div>
    `

    vehicleList.appendChild(vehicleItem)
  })
}

// Load complaints list
function loadComplaintsList() {
  const complaintsList = document.getElementById("complaintsList")
  if (!complaintsList) return

  complaintsList.innerHTML = ""

  const recentComplaints = window.WaaManApp.complaints.slice(0, 3)

  recentComplaints.forEach((complaint) => {
    const complaintItem = document.createElement("div")
    complaintItem.className =
      "p-4 border border-gray-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 cursor-pointer bg-white/50 backdrop-blur-sm"

    complaintItem.innerHTML = `
      <div class="flex items-start justify-between mb-3">
        <h4 class="font-bold text-gray-900">${complaint.title}</h4>
        <span class="px-3 py-1 rounded-full text-xs font-semibold ${window.WaaManApp.getPriorityColor(complaint.priority)}">
          ${complaint.priority.toUpperCase()}
        </span>
      </div>
      <p class="text-sm text-gray-600 mb-3">${complaint.description}</p>
      <div class="flex items-center justify-between">
        <span class="text-xs text-gray-500">${window.WaaManApp.formatDate(complaint.date)}</span>
        <span class="px-3 py-1 rounded-full text-xs font-semibold ${window.WaaManApp.getStatusColor(complaint.status)}">
          ${complaint.status.replace("-", " ").toUpperCase()}
        </span>
      </div>
    `

    complaintsList.appendChild(complaintItem)
  })
}

// Start real-time updates
function startRealTimeUpdates() {
  setInterval(() => {
    updateVehicleLocations()
    checkForNotifications()
  }, 5000)

  setInterval(() => {
    updateQuickStats()
  }, 30000)
}

// Update vehicle locations
function updateVehicleLocations() {
  window.WaaManApp.vehicles.forEach((vehicle) => {
    if (vehicle.status === "active") {
      vehicle.location.lat += (Math.random() - 0.5) * 0.001
      vehicle.location.lng += (Math.random() - 0.5) * 0.001

      const etas = ["5 min", "8 min", "12 min", "15 min", "20 min"]
      vehicle.eta = etas[Math.floor(Math.random() * etas.length)]
    }
  })

  if (map) {
    loadVehicleMarkers()
  }
  loadVehicleList()
}

// Check for notifications
function checkForNotifications() {
  if (Math.random() > 0.9) {
    const messages = [
      "Vehicle approaching your area in 5 minutes",
      "Collection completed for your route",
      "Your complaint has been updated",
      "New collection schedule available",
    ]

    const message = messages[Math.floor(Math.random() * messages.length)]
    addNotification(message)
  }
}

// Setup notifications
function setupNotifications() {
  if ("Notification" in window && Notification.permission === "default") {
    Notification.requestPermission()
  }
  loadNotifications()
}

// Add notification
function addNotification(message) {
  const notification = {
    id: Date.now(),
    message: message,
    timestamp: new Date(),
    read: false,
  }

  window.WaaManApp.notifications.unshift(notification)
  updateNotificationBadge()

  if ("Notification" in window && Notification.permission === "granted") {
    new Notification("WaaMan Alert", {
      body: message,
      icon: "/favicon.ico",
    })
  }

  loadNotifications()
}

// Load notifications
function loadNotifications() {
  const notificationList = document.getElementById("notificationList")
  if (!notificationList) return

  notificationList.innerHTML = ""

  if (window.WaaManApp.notifications.length === 0) {
    notificationList.innerHTML = `
      <div class="p-6 text-center text-gray-500">
        <i class="fas fa-bell-slash text-3xl mb-3"></i>
        <p>No notifications</p>
      </div>
    `
    return
  }

  window.WaaManApp.notifications.slice(0, 5).forEach((notification) => {
    const notificationItem = document.createElement("div")
    notificationItem.className = `p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${!notification.read ? "bg-blue-50/50" : ""}`

    notificationItem.innerHTML = `
      <div class="flex items-start space-x-3">
        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 ${notification.read ? "opacity-0" : ""}"></div>
        <div class="flex-1">
          <p class="text-sm text-gray-900 font-medium">${notification.message}</p>
          <p class="text-xs text-gray-500 mt-1">${window.WaaManApp.formatTime(notification.timestamp)}</p>
        </div>
      </div>
    `

    notificationItem.addEventListener("click", () => {
      notification.read = true
      updateNotificationBadge()
      loadNotifications()
    })

    notificationList.appendChild(notificationItem)
  })
}

// Update notification badge
function updateNotificationBadge() {
  const badge = document.getElementById("notificationBadge")
  const unreadCount = window.WaaManApp.notifications.filter((n) => !n.read).length

  if (badge) {
    if (unreadCount > 0) {
      badge.textContent = unreadCount
      badge.classList.remove("hidden")
    } else {
      badge.classList.add("hidden")
    }
  }
}

// Modal functions
function openComplaintModal() {
  const modal = document.getElementById("complaintModal")
  if (modal) {
    modal.classList.remove("hidden")
  }
}

function closeComplaintModal() {
  const modal = document.getElementById("complaintModal")
  if (modal) {
    modal.classList.add("hidden")
  }
}

// Handle complaint submission
function handleComplaintSubmission(e) {
  e.preventDefault()

  const formData = new FormData(e.target)
  const complaint = {
    title: formData.get("type") || "New Complaint",
    description: formData.get("description") || "",
    location: formData.get("location") || "",
    priority: formData.get("priority") || "medium",
  }

  // Use API to create complaint
  window.WaaManAPI.createComplaint(complaint)
    .then((response) => {
      if (response.success) {
        window.WaaManApp.complaints.unshift(response.complaint)
        closeComplaintModal()
        window.WaaManApp.showNotification("Complaint submitted successfully!", "success")
        loadComplaintsList()
        updateQuickStats()
        e.target.reset()
      }
    })
    .catch((error) => {
      window.WaaManApp.showNotification("Failed to submit complaint: " + error.message, "error")
    })
}

// Quick action functions
function requestPickup() {
  window.WaaManApp.showNotification("Pickup request submitted successfully!", "success")
}

function trackVehicle() {
  if (map && window.WaaManApp.vehicles.length > 0) {
    const activeVehicle = window.WaaManApp.vehicles.find((v) => v.status === "active")
    if (activeVehicle) {
      map.setView([activeVehicle.location.lat, activeVehicle.location.lng], 16)
      window.WaaManApp.showNotification(`Tracking ${activeVehicle.name}`, "info")
    }
  }
}

// Logout function
function logout() {
  localStorage.removeItem("waamanUser")
  window.location.href = "index.html"
}

// Add real-time event listeners
document.addEventListener("DOMContentLoaded", () => {
  // Listen for real-time vehicle updates
  window.addEventListener("vehiclesUpdate", (event) => {
    window.WaaManApp.vehicles = event.detail
    if (map) {
      loadVehicleMarkers()
    }
    loadVehicleList()
    updateQuickStats()
  })

  // Listen for vehicle location updates
  window.addEventListener("vehicleLocationUpdate", (event) => {
    const { vehicleId, location } = event.detail
    const vehicle = window.WaaManApp.vehicles.find((v) => v.id === vehicleId)
    if (vehicle) {
      vehicle.location = location
      if (map) {
        loadVehicleMarkers()
      }
      loadVehicleList()
    }
  })

  // Listen for new complaints
  window.addEventListener("newComplaint", (event) => {
    window.WaaManApp.complaints.unshift(event.detail)
    loadComplaintsList()
    updateQuickStats()
    addNotification("New complaint received")
  })

  // Listen for complaint updates
  window.addEventListener("complaintUpdate", (event) => {
    const updatedComplaint = event.detail
    const index = window.WaaManApp.complaints.findIndex((c) => c.id === updatedComplaint.id)
    if (index !== -1) {
      window.WaaManApp.complaints[index] = updatedComplaint
      loadComplaintsList()
      updateQuickStats()
      addNotification(`Complaint #${updatedComplaint.id} updated`)
    }
  })

  // Listen for emergency alerts
  window.addEventListener("emergencyAlert", (event) => {
    const alert = event.detail
    addNotification(`Emergency Alert: ${alert.message}`)
    // Show emergency modal or notification
    showEmergencyAlert(alert)
  })
})

// Emergency alert handler
function showEmergencyAlert(alert) {
  const emergencyModal = document.createElement("div")
  emergencyModal.className = "fixed inset-0 bg-red-600 bg-opacity-90 flex items-center justify-center z-50"
  emergencyModal.innerHTML = `
    <div class="bg-white rounded-3xl p-8 max-w-md mx-4 text-center">
      <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <i class="fas fa-exclamation-triangle text-red-600 text-3xl"></i>
      </div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">Emergency Alert</h3>
      <p class="text-gray-600 mb-6">${alert.message}</p>
      <p class="text-sm text-gray-500 mb-6">From: ${alert.userName}</p>
      <button onclick="this.parentElement.parentElement.remove()" 
              class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-xl font-semibold">
        Acknowledge
      </button>
    </div>
  `

  document.body.appendChild(emergencyModal)

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (emergencyModal.parentElement) {
      emergencyModal.remove()
    }
  }, 10000)
}
