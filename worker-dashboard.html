<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Worker Dashboard - WaaMan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="font-inter bg-gray-50">
     Header 
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-recycle text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">WaaMan</h1>
                        <p class="text-xs text-gray-500">Worker Dashboard</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                     Emergency Button 
                    <button onclick="triggerSOS()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        SOS
                    </button>
                    
                     Worker Status 
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600 hidden md:block">On Duty</span>
                    </div>
                    
                     User Menu 
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="hidden md:block text-sm font-medium text-gray-700">Mike Johnson</span>
                        <button onclick="logout()" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

     Main Content 
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
         Welcome Section 
        <div class="mb-8">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Good morning, Mike!</h2>
            <p class="text-gray-600">Your shift started at 8:00 AM • Vehicle: Truck A-102</p>
        </div>

         Status Cards 
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Routes Completed</p>
                        <p class="text-2xl font-bold text-green-600" id="completedRoutes">2</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Current Route</p>
                        <p class="text-2xl font-bold text-blue-600" id="currentRoute">Route C</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-route text-blue-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Fuel Level</p>
                        <p class="text-2xl font-bold text-orange-600" id="fuelLevel">75%</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-gas-pump text-orange-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Hours Worked</p>
                        <p class="text-2xl font-bold text-purple-600" id="hoursWorked">4.5</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-clock text-purple-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
             Left Column 
            <div class="lg:col-span-2 space-y-8">
                 Vehicle Status 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Vehicle Status - Truck A-102</h3>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">Operational</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-tachometer-alt text-blue-600 text-2xl"></i>
                                </div>
                                <p class="text-sm text-gray-600">Engine Status</p>
                                <p class="text-lg font-bold text-gray-900">Excellent</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-gas-pump text-green-600 text-2xl"></i>
                                </div>
                                <p class="text-sm text-gray-600">Fuel Level</p>
                                <p class="text-lg font-bold text-gray-900">75%</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-wrench text-orange-600 text-2xl"></i>
                                </div>
                                <p class="text-sm text-gray-600">Last Service</p>
                                <p class="text-lg font-bold text-gray-900">Jan 15</p>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex space-x-4">
                            <button onclick="reportIssue()" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                Report Issue
                            </button>
                            <button onclick="requestMaintenance()" class="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors">
                                <i class="fas fa-tools mr-2"></i>
                                Request Maintenance
                            </button>
                        </div>
                    </div>
                </div>

                 Today's Routes 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900">Today's Routes</h3>
                    </div>
                    <div class="p-6">
                        <div id="routesList" class="space-y-4">
                             Routes will be populated here 
                        </div>
                    </div>
                </div>
            </div>

             Right Column 
            <div class="space-y-8">
                 Emergency Actions 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900">Emergency Actions</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <button onclick="triggerSOS()" class="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-red-600 hover:to-red-700 transition-all transform hover:scale-105">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Emergency SOS
                        </button>
                        <button onclick="findNearbyVehicles()" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-blue-600 hover:to-blue-700 transition-all transform hover:scale-105">
                            <i class="fas fa-search-location mr-2"></i>
                            Find Nearby Vehicles
                        </button>
                        <button onclick="contactDispatch()" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-green-600 hover:to-green-700 transition-all transform hover:scale-105">
                            <i class="fas fa-phone mr-2"></i>
                            Contact Dispatch
                        </button>
                    </div>
                </div>

                 Team Communication 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Team Chat</h3>
                            <span class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="chatMessages" class="space-y-3 mb-4 max-h-48 overflow-y-auto">
                             Chat messages will be populated here 
                        </div>
                        <div class="flex space-x-2">
                            <input type="text" id="chatInput" placeholder="Type a message..." class="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <button onclick="sendMessage()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>

                 Quick Stats 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900">Performance</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Routes This Week</span>
                            <span class="font-bold text-gray-900">12/15</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-emerald-500 h-2 rounded-full" style="width: 80%"></div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">On-Time Performance</span>
                            <span class="font-bold text-green-600">94%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 94%"></div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Fuel Efficiency</span>
                            <span class="font-bold text-blue-600">Good</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

     SOS Modal 
    <div id="sosModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-exclamation-triangle text-red-600 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Emergency Alert</h3>
                <p class="text-gray-600 mb-6">This will send an immediate alert to dispatch and nearby vehicles. Are you sure you want to proceed?</p>
                
                <div class="flex space-x-4">
                    <button onclick="closeSosModal()" class="flex-1 py-3 px-4 border border-gray-300 rounded-xl font-semibold text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button onclick="confirmSOS()" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors">
                        Send SOS Alert
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/worker-dashboard.js"></script>
</body>
</html>
