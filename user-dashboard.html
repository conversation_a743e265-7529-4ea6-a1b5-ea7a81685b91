<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - WaaMan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="font-inter bg-gray-50">
     <!-- Header -->
<header class="bg-white/95 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-recycle text-white text-xl"></i>
                    </div>
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                    <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">WaaMan</h1>
                    <p class="text-xs text-gray-500 font-medium tracking-wide">CITIZEN PORTAL</p>
                </div>
            </div>
            
            <div class="flex items-center space-x-6">
                <!-- Enhanced Notifications -->
                <div class="relative">
                    <button id="notificationBtn" class="relative p-3 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 group">
                        <i class="fas fa-bell text-lg group-hover:scale-110 transition-transform"></i>
                        <span id="notificationBadge" class="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg animate-pulse hidden">0</span>
                    </button>
                    
                    <!-- Enhanced Notification Dropdown -->
                    <div id="notificationDropdown" class="absolute right-0 mt-4 w-96 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200/50 hidden z-50 overflow-hidden">
                        <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-emerald-50 to-teal-50">
                            <div class="flex items-center justify-between">
                                <h3 class="font-bold text-gray-900 text-lg">Notifications</h3>
                                <button class="text-emerald-600 hover:text-emerald-700 text-sm font-medium">Mark all read</button>
                            </div>
                        </div>
                        <div id="notificationList" class="max-h-80 overflow-y-auto custom-scrollbar">
                            <!-- Notifications will be populated here -->
                        </div>
                        <div class="p-4 border-t border-gray-100 bg-gray-50/50">
                            <button class="w-full text-emerald-600 hover:text-emerald-700 font-medium text-sm py-2">
                                View All Notifications
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Enhanced User Menu -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="hidden md:block">
                        <p class="text-sm font-semibold text-gray-900">John Doe</p>
                        <p class="text-xs text-gray-500">Premium Member</p>
                    </div>
                    <button onclick="logout()" class="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-xl transition-all duration-300">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</header>

     <!-- Main Content -->
<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Welcome Section -->
    <div class="mb-12">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h2 class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                    Welcome back, John! 👋
                </h2>
                <p class="text-lg text-gray-600">Track your waste collection vehicles and manage complaints efficiently</p>
            </div>
            <div class="hidden md:flex items-center space-x-4">
                <div class="text-right">
                    <p class="text-sm text-gray-500">Last updated</p>
                    <p class="text-sm font-medium text-gray-900" id="lastUpdated">Just now</p>
                </div>
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            </div>
        </div>
        
        <!-- Enhanced Quick Actions Bar -->
        <div class="flex flex-wrap gap-4">
            <button onclick="openComplaintModal()" class="group bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-6 py-3 rounded-2xl font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center">
                <i class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform"></i>
                File Complaint
            </button>
            <button onclick="requestPickup()" class="group bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-6 py-3 rounded-2xl font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center">
                <i class="fas fa-calendar-plus mr-2 group-hover:scale-110 transition-transform"></i>
                Request Pickup
            </button>
            <button onclick="trackVehicle()" class="group bg-gradient-to-r from-purple-500 to-indigo-600 text-white px-6 py-3 rounded-2xl font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center">
                <i class="fas fa-search-location mr-2 group-hover:bounce transition-transform"></i>
                Track Vehicle
            </button>
        </div>
    </div>

    <!-- Enhanced Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
        <div class="group relative">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
            <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                        <i class="fas fa-truck text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">Vehicles Nearby</p>
                        <p class="text-3xl font-bold text-blue-600" id="nearbyVehicles">3</p>
                    </div>
                </div>
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>+2 from yesterday</span>
                </div>
            </div>
        </div>
        
        <div class="group relative">
            <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
            <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">Next Collection</p>
                        <p class="text-3xl font-bold text-emerald-600" id="nextCollection">12 min</p>
                    </div>
                </div>
                <div class="flex items-center text-sm text-emerald-600">
                    <i class="fas fa-check mr-1"></i>
                    <span>On schedule</span>
                </div>
            </div>
        </div>
        
        <div class="group relative">
            <div class="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
            <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                        <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">My Complaints</p>
                        <p class="text-3xl font-bold text-orange-600" id="myComplaints">2</p>
                    </div>
                </div>
                <div class="flex items-center text-sm text-orange-600">
                    <i class="fas fa-clock mr-1"></i>
                    <span>1 pending review</span>
                </div>
            </div>
        </div>
        
        <div class="group relative">
            <div class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
            <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">Resolved</p>
                        <p class="text-3xl font-bold text-green-600" id="resolvedComplaints">8</p>
                    </div>
                </div>
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>+3 this month</span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Enhanced Live Vehicle Map -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-3xl blur-xl"></div>
                <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
                    <div class="p-8 border-b border-gray-100/50 bg-gradient-to-r from-emerald-50/50 to-teal-50/50">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-2">Live Vehicle Tracking</h3>
                                <p class="text-gray-600">Real-time GPS monitoring of waste collection vehicles</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm font-medium text-gray-700">Live</span>
                                </div>
                                <button class="p-2 hover:bg-white/50 rounded-xl transition-colors">
                                    <i class="fas fa-expand text-gray-600"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <div id="map" class="h-96 rounded-2xl bg-gray-100 relative overflow-hidden shadow-inner">
                            <!-- Map will be initialized here -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
                                        <i class="fas fa-map-marked-alt text-white text-2xl"></i>
                                    </div>
                                    <p class="text-gray-600 font-medium">Interactive map loading...</p>
                                    <p class="text-sm text-gray-500 mt-1">GPS tracking will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Vehicle List -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-3xl blur-xl"></div>
                <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20">
                    <div class="p-8 border-b border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <h3 class="text-2xl font-bold text-gray-900">Nearby Vehicles</h3>
                            <div class="flex items-center space-x-2">
                                <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-xl font-medium hover:bg-blue-200 transition-colors">
                                    <i class="fas fa-filter mr-2"></i>Filter
                                </button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                                    <i class="fas fa-sort mr-2"></i>Sort
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <div id="vehicleList" class="space-y-6">
                            <!-- Vehicle items will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-8">
            <!-- Enhanced Recent Complaints -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 rounded-3xl blur-xl"></div>
                <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20">
                    <div class="p-6 border-b border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Recent Complaints</h3>
                            <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium flex items-center">
                                View All
                                <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="complaintsList" class="space-y-4">
                            <!-- Complaints will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Collection Schedule -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-3xl blur-xl"></div>
                <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20">
                    <div class="p-6 border-b border-gray-100/50">
                        <h3 class="text-xl font-bold text-gray-900">Collection Schedule</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="group/item flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl border border-blue-200/50 hover:shadow-lg transition-all duration-300">
                                <div class="flex items-center space-x-4">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                                    <div>
                                        <p class="font-semibold text-gray-900">General Waste</p>
                                        <p class="text-sm text-gray-600">Truck A-102 • Mike Johnson</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">Today</p>
                                    <p class="text-sm text-blue-600">2:00 PM</p>
                                </div>
                            </div>
                            
                            <div class="group/item flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200/50 hover:shadow-lg transition-all duration-300">
                                <div class="flex items-center space-x-4">
                                    <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Recyclables</p>
                                        <p class="text-sm text-gray-600">Truck B-205 • Sarah Davis</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">Tomorrow</p>
                                    <p class="text-sm text-green-600">10:00 AM</p>
                                </div>
                            </div>
                            
                            <div class="group/item flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl border border-yellow-200/50 hover:shadow-lg transition-all duration-300">
                                <div class="flex items-center space-x-4">
                                    <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Organic Waste</p>
                                        <p class="text-sm text-gray-600">Van C-301 • Tom Wilson</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">Friday</p>
                                    <p class="text-sm text-yellow-600">9:00 AM</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Quick Tips -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
                <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20">
                    <div class="p-6 border-b border-gray-100/50">
                        <h3 class="text-xl font-bold text-gray-900">Quick Tips</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3 p-3 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl">
                                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-lightbulb text-white text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Enable notifications</p>
                                    <p class="text-xs text-gray-600">Get alerts when vehicles approach your area</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3 p-3 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl">
                                <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-camera text-white text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Add photos to complaints</p>
                                    <p class="text-xs text-gray-600">Visual evidence helps resolve issues faster</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Enhanced Complaint Modal -->
<div id="complaintModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-white/95 backdrop-blur-xl rounded-3xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto shadow-2xl border border-white/20">
        <div class="flex items-center justify-between mb-8">
            <div>
                <h3 class="text-3xl font-bold text-gray-900 mb-2">File New Complaint</h3>
                <p class="text-gray-600">Help us resolve your waste management concerns quickly</p>
            </div>
            <button onclick="closeComplaintModal()" class="p-3 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-2xl transition-all duration-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="complaintForm" class="space-y-8">
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Complaint Type</label>
                    <select class="w-full px-6 py-4 bg-gray-50/50 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <option>Missed Collection</option>
                        <option>Damaged Bin</option>
                        <option>Overflowing Container</option>
                        <option>Improper Collection</option>
                        <option>Other</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Priority Level</label>
                    <select class="w-full px-6 py-4 bg-gray-50/50 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <option>Low</option>
                        <option>Medium</option>
                        <option>High</option>
                        <option>Urgent</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Description</label>
                <textarea rows="4" class="w-full px-6 py-4 bg-gray-50/50 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Describe the issue in detail..."></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Location</label>
                <input type="text" class="w-full px-6 py-4 bg-gray-50/50 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Enter your address or use current location">
            </div>
            
            <div>
                <label class="block text-sm font-semibold text-gray-700 mb-3">Attach Photos (Optional)</label>
                <div class="border-2 border-dashed border-gray-300 rounded-2xl p-8 text-center hover:border-blue-400 transition-colors cursor-pointer bg-gray-50/50">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cloud-upload-alt text-white text-2xl"></i>
                    </div>
                    <p class="text-gray-600 font-medium mb-2">Click to upload or drag and drop</p>
                    <p class="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                    <input type="file" class="hidden" accept="image/*" multiple>
                </div>
            </div>
            
            <div class="flex space-x-4 pt-6">
                <button type="button" onclick="closeComplaintModal()" class="flex-1 py-4 px-6 border border-gray-300 rounded-2xl font-semibold text-gray-700 hover:bg-gray-50 transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-4 px-6 rounded-2xl font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300">
                    Submit Complaint
                </button>
            </div>
        </form>
    </div>
</div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="scripts/user-dashboard.js"></script>
</body>
</html>
