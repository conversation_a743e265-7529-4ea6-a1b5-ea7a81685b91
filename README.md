# WaaMan - Smart Waste Management System

A comprehensive waste management system built with Node.js, Express, and Socket.IO for real-time tracking and communication.

## 🚀 Features

- **Real-time Vehicle Tracking** - GPS-based location updates
- **Complaint Management** - Submit and track waste collection complaints
- **Emergency SOS System** - Instant alerts for worker safety
- **Multi-role Authentication** - User, Worker, and Manager portals
- **Live Notifications** - Real-time updates via WebSocket
- **Professional UI/UX** - Modern design with Tailwind CSS

## 🛠️ Technology Stack

- **Backend**: Node.js, Express.js
- **Real-time**: Socket.IO
- **Frontend**: HTML5, CSS3, Tai<PERSON><PERSON> CSS, Vanilla JavaScript
- **Authentication**: JWT (JSON Web Tokens)
- **Maps**: Leaflet.js
- **Icons**: Font Awesome

## 📦 Installation

1. **Clone the repository**
   \`\`\`bash
   git clone <repository-url>
   cd waaman-waste-management
   \`\`\`

2. **Install dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Start the server**
   \`\`\`bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   \`\`\`

4. **Access the application**
   - Open your browser and go to `http://localhost:3000`

## 🔐 Demo Credentials

### User Portal
- **Email**: <EMAIL>
- **Password**: waaman123

### Worker Portal
- **Email**: <EMAIL>
- **Password**: waaman123

### Manager Portal
- **Email**: <EMAIL>
- **Password**: waaman123

## 📁 Project Structure

\`\`\`
waaman-waste-management/
├── server.js                 # Main Node.js server
├── package.json             # Dependencies and scripts
├── index.html              # Landing page
├── user-dashboard.html     # User interface
├── worker-dashboard.html   # Worker interface
├── manager-dashboard.html  # Manager interface
├── scripts/
│   ├── main.js            # Core JavaScript
│   ├── api.js             # API client
│   └── user-dashboard.js  # User dashboard logic
└── styles/
    └── main.css           # Custom styles
\`\`\`

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - User login

### Vehicles
- `GET /api/vehicles` - Get all vehicles
- `GET /api/vehicles/:id` - Get specific vehicle
- `PUT /api/vehicles/:id/location` - Update vehicle location
- `PUT /api/vehicles/:id/status` - Update vehicle status

### Complaints
- `GET /api/complaints` - Get complaints
- `POST /api/complaints` - Create new complaint
- `PUT /api/complaints/:id` - Update complaint

### Emergency
- `POST /api/emergency/sos` - Send SOS alert

### Notifications
- `GET /api/notifications` - Get user notifications

## 🔄 Real-time Features

The application uses Socket.IO for real-time communication:

- **Vehicle Location Updates** - Live GPS tracking
- **Complaint Notifications** - Instant alerts for new complaints
- **Emergency Alerts** - SOS broadcasts to all managers
- **Chat System** - Team communication
- **Status Updates** - Real-time vehicle and complaint status changes

## 🚀 Deployment

### Local Development
\`\`\`bash
npm run dev
\`\`\`

### Production
\`\`\`bash
npm start
\`\`\`

### Environment Variables
Create a `.env` file for production:
\`\`\`env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=production
\`\`\`

## 🔧 Configuration

The server can be configured through environment variables:

- `PORT` - Server port (default: 3000)
- `JWT_SECRET` - JWT signing secret
- `NODE_ENV` - Environment mode

## 📱 Mobile Responsive

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🛡️ Security Features

- JWT-based authentication
- Password hashing with bcrypt
- CORS protection
- Input validation
- Secure headers

## 🔮 Future Enhancements

- Database integration (MongoDB/PostgreSQL)
- Push notifications
- Advanced analytics
- Route optimization algorithms
- Mobile app development
- Integration with IoT sensors

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

## 📄 License

This project is licensed under the MIT License.
