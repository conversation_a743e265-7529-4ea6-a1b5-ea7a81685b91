// WaaMan Application - Main JavaScript

// Global variables
let currentUser = null
let notifications = []
let vehicles = []
let complaints = []

// Initialize application
document.addEventListener("DOMContentLoaded", () => {
  initializeApp()
  setupEventListeners()
  loadMockData()
})

// Initialize the application
function initializeApp() {
  console.log("WaaMan Application Initialized")

  // Check for saved user session
  const savedUser = localStorage.getItem("waamanUser")
  if (savedUser) {
    currentUser = JSON.parse(savedUser)
    redirectToDashboard(currentUser.role)
  }

  // Initialize smooth scrolling
  initializeSmoothScrolling()

  // Initialize animations
  initializeAnimations()
}

// Setup event listeners
function setupEventListeners() {
  // Mobile menu toggle
  const mobileMenuBtn = document.querySelector(".mobile-menu-btn")
  if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener("click", toggleMobileMenu)
  }

  // Form submissions
  const forms = document.querySelectorAll("form")
  forms.forEach((form) => {
    form.addEventListener("submit", handleFormSubmission)
  })

  // Keyboard navigation
  document.addEventListener("keydown", handleKeyboardNavigation)
}

// Update the loginUser function to use the API
function loginUser(userType) {
  const email = `${userType}@waaman.com`
  const password = "waaman123"

  // Show loading animation
  showLoadingAnimation()

  // Use API for authentication
  window.WaaManAPI.login(email, password)
    .then((response) => {
      if (response.success) {
        currentUser = response.user
        hideLoadingAnimation()
        redirectToDashboard(userType)
      }
    })
    .catch((error) => {
      hideLoadingAnimation()
      showNotification("Login failed: " + error.message, "error")
    })
}

// Add API integration to existing functions
function loadMockData() {
  // Load data from API instead of mock data
  if (window.WaaManAPI) {
    window.WaaManAPI.getVehicles()
      .then((response) => {
        if (response.success) {
          vehicles = response.vehicles
        }
      })
      .catch((error) => {
        console.error("Failed to load vehicles:", error)
        // Fallback to mock data
        vehicles = [
          {
            id: 1,
            name: "Truck A-102",
            type: "compactor",
            status: "active",
            location: { lat: 40.7128, lng: -74.006 },
            driver: "Mike Johnson",
            fuelLevel: 75,
            eta: "12 min",
            route: "Route A",
          },
          {
            id: 2,
            name: "Truck B-205",
            type: "truck",
            status: "active",
            location: { lat: 40.7589, lng: -73.9851 },
            driver: "Sarah Davis",
            fuelLevel: 60,
            eta: "8 min",
            route: "Route B",
          },
          {
            id: 3,
            name: "Van C-301",
            type: "van",
            status: "maintenance",
            location: { lat: 40.7505, lng: -73.9934 },
            driver: "Tom Wilson",
            fuelLevel: 45,
            eta: "N/A",
            route: "Maintenance",
          },
        ]
      })

    window.WaaManAPI.getComplaints()
      .then((response) => {
        if (response.success) {
          complaints = response.complaints
        }
      })
      .catch((error) => {
        console.error("Failed to load complaints:", error)
        // Fallback to mock data
        complaints = [
          {
            id: 1,
            title: "Missed Collection",
            description: "Garbage was not collected on scheduled day",
            status: "pending",
            priority: "high",
            date: "2024-01-15",
            location: "123 Main St",
            userId: "user1",
            assignedTo: null,
          },
          {
            id: 2,
            title: "Overflowing Bin",
            description: "Public bin is overflowing and needs immediate attention",
            status: "in-progress",
            priority: "medium",
            date: "2024-01-14",
            location: "456 Oak Ave",
            userId: "user2",
            assignedTo: "worker1",
          },
        ]
      })
  }

  // Mock notifications (keep as fallback)
  notifications = [
    {
      id: 1,
      message: "Vehicle Truck A-102 is approaching your area",
      type: "vehicle_approaching",
      timestamp: new Date(),
      read: false,
    },
    {
      id: 2,
      message: "Your complaint #1001 has been resolved",
      type: "complaint_update",
      timestamp: new Date(Date.now() - 3600000),
      read: false,
    },
  ]
}

// Get user name based on type
function getUserName(userType) {
  const names = {
    user: "John Doe",
    worker: "Mike Johnson",
    manager: "Sarah Wilson",
  }
  return names[userType] || "User"
}

// Redirect to appropriate dashboard
function redirectToDashboard(userType) {
  const dashboards = {
    user: "user-dashboard.html",
    worker: "worker-dashboard.html",
    manager: "manager-dashboard.html",
  }

  if (dashboards[userType]) {
    window.location.href = dashboards[userType]
  }
}

// Logout function
function logout() {
  currentUser = null
  localStorage.removeItem("waamanUser")
  window.location.href = "index.html"
}

// Show loading animation
function showLoadingAnimation() {
  const loader = document.createElement("div")
  loader.id = "loadingOverlay"
  loader.className = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  loader.innerHTML = `
    <div class="bg-white rounded-3xl p-8 text-center shadow-2xl">
      <div class="spinner-professional mx-auto mb-4"></div>
      <p class="text-gray-700 font-medium">Logging you in...</p>
    </div>
  `
  document.body.appendChild(loader)
}

// Hide loading animation
function hideLoadingAnimation() {
  const loader = document.getElementById("loadingOverlay")
  if (loader) {
    loader.remove()
  }
}

// Initialize smooth scrolling
function initializeSmoothScrolling() {
  const links = document.querySelectorAll('a[href^="#"]')
  links.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault()
      const target = document.querySelector(this.getAttribute("href"))
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        })
      }
    })
  })
}

// Initialize animations
function initializeAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-fade-in-up")
      }
    })
  }, observerOptions)

  const animateElements = document.querySelectorAll(".card-hover, .group")
  animateElements.forEach((el) => observer.observe(el))
}

// Handle form submissions
function handleFormSubmission(e) {
  e.preventDefault()
  const form = e.target
  const formData = new FormData(form)
  showNotification("Form submitted successfully!", "success")
}

// Show notification
function showNotification(message, type = "info") {
  const notification = document.createElement("div")
  notification.className = `fixed top-4 right-4 z-50 p-4 rounded-2xl shadow-2xl max-w-sm transform transition-all duration-300 translate-x-full`

  const bgColors = {
    success: "bg-green-500",
    error: "bg-red-500",
    warning: "bg-yellow-500",
    info: "bg-blue-500",
  }

  notification.classList.add(bgColors[type] || bgColors.info)
  notification.innerHTML = `
    <div class="flex items-center text-white">
      <i class="fas fa-${getNotificationIcon(type)} mr-3"></i>
      <span>${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
        <i class="fas fa-times"></i>
      </button>
    </div>
  `

  document.body.appendChild(notification)

  setTimeout(() => {
    notification.classList.remove("translate-x-full")
  }, 100)

  setTimeout(() => {
    notification.classList.add("translate-x-full")
    setTimeout(() => notification.remove(), 300)
  }, 5000)
}

// Get notification icon
function getNotificationIcon(type) {
  const icons = {
    success: "check-circle",
    error: "exclamation-circle",
    warning: "exclamation-triangle",
    info: "info-circle",
  }
  return icons[type] || icons.info
}

// Handle keyboard navigation
function handleKeyboardNavigation(e) {
  if (e.key === "Escape") {
    const modals = document.querySelectorAll('.modal, [id$="Modal"]')
    modals.forEach((modal) => {
      if (!modal.classList.contains("hidden")) {
        modal.classList.add("hidden")
      }
    })
  }
}

// Toggle mobile menu
function toggleMobileMenu() {
  const mobileMenu = document.getElementById("mobileMenu")
  if (mobileMenu) {
    mobileMenu.classList.toggle("hidden")
  }
}

// Utility functions
function formatDate(date) {
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

function formatTime(date) {
  return new Date(date).toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  })
}

function getStatusColor(status) {
  const colors = {
    active: "text-green-600 bg-green-100",
    maintenance: "text-yellow-600 bg-yellow-100",
    offline: "text-red-600 bg-red-100",
    pending: "text-yellow-600 bg-yellow-100",
    "in-progress": "text-blue-600 bg-blue-100",
    resolved: "text-green-600 bg-green-100",
  }
  return colors[status] || "text-gray-600 bg-gray-100"
}

function getPriorityColor(priority) {
  const colors = {
    high: "text-red-600 bg-red-100 border-red-200",
    medium: "text-yellow-600 bg-yellow-100 border-yellow-200",
    low: "text-blue-600 bg-blue-100 border-blue-200",
  }
  return colors[priority] || "text-gray-600 bg-gray-100 border-gray-200"
}

// Export functions for use in other scripts
window.WaaManApp = {
  loginUser,
  logout,
  showNotification,
  formatDate,
  formatTime,
  getStatusColor,
  getPriorityColor,
  vehicles,
  complaints,
  notifications,
}
