<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manager Dashboard - WaaMan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="font-inter bg-gray-50">
     Header 
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-recycle text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">WaaMan</h1>
                        <p class="text-xs text-gray-500">Manager Dashboard</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                     Alerts 
                    <div class="relative">
                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                        </button>
                    </div>
                    
                     User Menu 
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="hidden md:block text-sm font-medium text-gray-700">Sarah Wilson</span>
                        <button onclick="logout()" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

     Main Content 
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
         Welcome Section 
        <div class="mb-8">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Operations Overview</h2>
            <p class="text-gray-600">Monitor fleet performance, manage complaints, and optimize operations</p>
        </div>

         KPI Cards 
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Active Vehicles</p>
                        <p class="text-2xl font-bold text-blue-600" id="activeVehicles">12</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+2 from yesterday
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-truck text-blue-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Pending Complaints</p>
                        <p class="text-2xl font-bold text-red-600" id="pendingComplaints">8</p>
                        <p class="text-xs text-red-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+3 today
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Active Workers</p>
                        <p class="text-2xl font-bold text-green-600" id="activeWorkers">24</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-check mr-1"></i>All on duty
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Efficiency</p>
                        <p class="text-2xl font-bold text-purple-600" id="efficiency">94%</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+2% this week
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
             Left Column 
            <div class="lg:col-span-2 space-y-8">
                 Fleet Overview Map 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Fleet Overview</h3>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Active (12)</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Maintenance (2)</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Offline (1)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="fleetMap" class="h-80 rounded-xl bg-gray-100 relative overflow-hidden">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-map text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-600">Fleet tracking map loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                 Performance Analytics 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Performance Analytics</h3>
                            <select class="px-3 py-2 border border-gray-200 rounded-lg text-sm">
                                <option>Last 7 days</option>
                                <option>Last 30 days</option>
                                <option>Last 3 months</option>
                            </select>
                        </div>
                    </div>
                    <div class="p-6">
                        <canvas id="performanceChart" class="w-full h-64"></canvas>
                    </div>
                </div>

                 Vehicle Management 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Vehicle Management</h3>
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Vehicle
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="vehicleManagementList" class="space-y-4">
                             Vehicle management items will be populated here 
                        </div>
                    </div>
                </div>
            </div>

             Right Column 
            <div class="space-y-8">
                 Complaint Resolution Center 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Complaint Center</h3>
                            <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">8 Pending</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="complaintCenter" class="space-y-4 max-h-80 overflow-y-auto">
                             Complaints will be populated here 
                        </div>
                        <button class="w-full mt-4 text-blue-600 hover:text-blue-700 font-medium text-sm">
                            View All Complaints
                        </button>
                    </div>
                </div>

                 Worker Status 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900">Worker Status</h3>
                    </div>
                    <div class="p-6">
                        <div id="workerStatus" class="space-y-4">
                             Worker status items will be populated here 
                        </div>
                    </div>
                </div>

                 Emergency Alerts 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-bold text-gray-900">Emergency Alerts</h3>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-shield-alt text-green-600 text-2xl"></i>
                            </div>
                            <p class="text-gray-600">No active emergencies</p>
                            <p class="text-sm text-gray-500 mt-1">All workers are safe</p>
                        </div>
                    </div>
                </div>

                 Quick Actions 
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button onclick="broadcastMessage()" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-blue-600 hover:to-blue-700 transition-all">
                            <i class="fas fa-bullhorn mr-2"></i>
                            Broadcast Message
                        </button>
                        <button onclick="generateReport()" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-green-600 hover:to-green-700 transition-all">
                            <i class="fas fa-file-alt mr-2"></i>
                            Generate Report
                        </button>
                        <button onclick="scheduleRoute()" class="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-purple-600 hover:to-purple-700 transition-all">
                            <i class="fas fa-route mr-2"></i>
                            Schedule Route
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="scripts/manager-dashboard.js"></script>
</body>
</html>
