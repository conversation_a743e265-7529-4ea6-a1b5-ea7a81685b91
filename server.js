// WaaMan Node.js Server
const express = require("express")
const http = require("http")
const socketIo = require("socket.io")
const cors = require("cors")
const bodyParser = require("body-parser")
const path = require("path")
const jwt = require("jsonwebtoken")
const bcrypt = require("bcryptjs")

// Initialize Express app
const app = express()
const server = http.createServer(app)
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
})

// Configuration
const PORT = process.env.PORT || 3000
const JWT_SECRET = process.env.JWT_SECRET || "waaman-super-secret-key"

// Middleware
app.use(cors())
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: true }))
app.use(express.static(path.join(__dirname)))

// Mock Database (In production, use MongoDB, PostgreSQL, etc.)
const users = [
  {
    id: 1,
    email: "<EMAIL>",
    password: bcrypt.hashSync("waaman123", 10),
    role: "user",
    name: "<PERSON>e",
  },
  {
    id: 2,
    email: "<EMAIL>",
    password: bcrypt.hashSync("waaman123", 10),
    role: "worker",
    name: "Mike Johnson",
  },
  {
    id: 3,
    email: "<EMAIL>",
    password: bcrypt.hashSync("waaman123", 10),
    role: "manager",
    name: "Sarah Wilson",
  },
]

const vehicles = [
  {
    id: 1,
    name: "Truck A-102",
    type: "compactor",
    status: "active",
    location: { lat: 40.7128, lng: -74.006 },
    driver: "Mike Johnson",
    fuelLevel: 75,
    eta: "12 min",
    route: "Route A",
    lastUpdated: new Date(),
  },
  {
    id: 2,
    name: "Truck B-205",
    type: "truck",
    status: "active",
    location: { lat: 40.7589, lng: -73.9851 },
    driver: "Sarah Davis",
    fuelLevel: 60,
    eta: "8 min",
    route: "Route B",
    lastUpdated: new Date(),
  },
  {
    id: 3,
    name: "Van C-301",
    type: "van",
    status: "maintenance",
    location: { lat: 40.7505, lng: -73.9934 },
    driver: "Tom Wilson",
    fuelLevel: 45,
    eta: "N/A",
    route: "Maintenance",
    lastUpdated: new Date(),
  },
]

const complaints = [
  {
    id: 1,
    title: "Missed Collection",
    description: "Garbage was not collected on scheduled day",
    status: "pending",
    priority: "high",
    date: "2024-01-15",
    location: "123 Main St",
    userId: 1,
    assignedTo: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    title: "Overflowing Bin",
    description: "Public bin is overflowing and needs immediate attention",
    status: "in-progress",
    priority: "medium",
    date: "2024-01-14",
    location: "456 Oak Ave",
    userId: 1,
    assignedTo: 2,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

const notifications = []

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}

// Routes

// Serve main page
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "index.html"))
})

// Authentication routes
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    // Find user
    const user = users.find((u) => u.email === email)
    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    // Check password
    const validPassword = await bcrypt.compare(password, user.password)
    if (!validPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    // Generate JWT token
    const token = jwt.sign({ id: user.id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: "24h" })

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Vehicle routes
app.get("/api/vehicles", authenticateToken, (req, res) => {
  res.json({
    success: true,
    vehicles: vehicles,
  })
})

app.get("/api/vehicles/:id", authenticateToken, (req, res) => {
  const vehicle = vehicles.find((v) => v.id === Number.parseInt(req.params.id))
  if (!vehicle) {
    return res.status(404).json({ error: "Vehicle not found" })
  }
  res.json({ success: true, vehicle })
})

app.put("/api/vehicles/:id/location", authenticateToken, (req, res) => {
  const { lat, lng } = req.body
  const vehicle = vehicles.find((v) => v.id === Number.parseInt(req.params.id))

  if (!vehicle) {
    return res.status(404).json({ error: "Vehicle not found" })
  }

  vehicle.location = { lat, lng }
  vehicle.lastUpdated = new Date()

  // Broadcast location update to all connected clients
  io.emit("vehicle:location", {
    vehicleId: vehicle.id,
    location: vehicle.location,
    timestamp: vehicle.lastUpdated,
  })

  res.json({ success: true, vehicle })
})

app.put("/api/vehicles/:id/status", authenticateToken, (req, res) => {
  const { status } = req.body
  const vehicle = vehicles.find((v) => v.id === Number.parseInt(req.params.id))

  if (!vehicle) {
    return res.status(404).json({ error: "Vehicle not found" })
  }

  vehicle.status = status
  vehicle.lastUpdated = new Date()

  // Broadcast status update
  io.emit("vehicle:status", {
    vehicleId: vehicle.id,
    status: vehicle.status,
    timestamp: vehicle.lastUpdated,
  })

  res.json({ success: true, vehicle })
})

// Complaint routes
app.get("/api/complaints", authenticateToken, (req, res) => {
  let userComplaints = complaints

  // Filter complaints based on user role
  if (req.user.role === "user") {
    userComplaints = complaints.filter((c) => c.userId === req.user.id)
  }

  res.json({
    success: true,
    complaints: userComplaints,
  })
})

app.post("/api/complaints", authenticateToken, (req, res) => {
  const { title, description, location, priority = "medium" } = req.body

  const newComplaint = {
    id: complaints.length + 1,
    title,
    description,
    location,
    priority,
    status: "pending",
    userId: req.user.id,
    assignedTo: null,
    date: new Date().toISOString().split("T")[0],
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  complaints.push(newComplaint)

  // Broadcast new complaint to managers
  io.emit("complaint:new", newComplaint)

  res.json({
    success: true,
    complaint: newComplaint,
  })
})

app.put("/api/complaints/:id", authenticateToken, (req, res) => {
  const { status, assignedTo, priority } = req.body
  const complaint = complaints.find((c) => c.id === Number.parseInt(req.params.id))

  if (!complaint) {
    return res.status(404).json({ error: "Complaint not found" })
  }

  // Update complaint
  if (status) complaint.status = status
  if (assignedTo) complaint.assignedTo = assignedTo
  if (priority) complaint.priority = priority
  complaint.updatedAt = new Date()

  // Broadcast complaint update
  io.emit("complaint:update", complaint)

  res.json({ success: true, complaint })
})

// Notification routes
app.get("/api/notifications", authenticateToken, (req, res) => {
  const userNotifications = notifications.filter((n) => n.userId === req.user.id || n.userId === "all")

  res.json({
    success: true,
    notifications: userNotifications,
  })
})

// Emergency routes
app.post("/api/emergency/sos", authenticateToken, (req, res) => {
  const { location, message } = req.body

  const sosAlert = {
    id: Date.now(),
    userId: req.user.id,
    userName: req.user.name || "Unknown User",
    location,
    message: message || "Emergency SOS Alert",
    timestamp: new Date(),
    type: "sos",
  }

  // Broadcast SOS alert to all managers and nearby workers
  io.emit("emergency:sos", sosAlert)

  // Add to notifications
  notifications.push({
    id: Date.now(),
    userId: "all",
    message: `Emergency SOS from ${sosAlert.userName}`,
    type: "emergency",
    timestamp: new Date(),
    read: false,
  })

  res.json({
    success: true,
    message: "SOS alert sent successfully",
    alert: sosAlert,
  })
})

// Real-time updates
setInterval(() => {
  // Simulate vehicle movement for active vehicles
  vehicles.forEach((vehicle) => {
    if (vehicle.status === "active") {
      // Small random movement
      vehicle.location.lat += (Math.random() - 0.5) * 0.001
      vehicle.location.lng += (Math.random() - 0.5) * 0.001

      // Update ETA randomly
      const etas = ["5 min", "8 min", "12 min", "15 min", "20 min"]
      vehicle.eta = etas[Math.floor(Math.random() * etas.length)]

      vehicle.lastUpdated = new Date()
    }
  })

  // Broadcast vehicle updates
  io.emit("vehicles:update", vehicles)
}, 10000) // Update every 10 seconds

// Socket.IO connection handling
io.on("connection", (socket) => {
  console.log("Client connected:", socket.id)

  // Join room based on user role
  socket.on("join", (userData) => {
    socket.join(userData.role)
    console.log(`User ${userData.name} joined ${userData.role} room`)
  })

  // Handle vehicle location updates from workers
  socket.on("update:location", (data) => {
    const { vehicleId, location } = data
    const vehicle = vehicles.find((v) => v.id === vehicleId)

    if (vehicle) {
      vehicle.location = location
      vehicle.lastUpdated = new Date()

      // Broadcast to all clients
      socket.broadcast.emit("vehicle:location", {
        vehicleId,
        location,
        timestamp: vehicle.lastUpdated,
      })
    }
  })

  // Handle chat messages
  socket.on("chat:message", (data) => {
    // Broadcast message to appropriate room
    socket.to(data.room || "general").emit("chat:message", {
      ...data,
      timestamp: new Date(),
    })
  })

  socket.on("disconnect", () => {
    console.log("Client disconnected:", socket.id)
  })
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: "Something went wrong!" })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: "Route not found" })
})

// Start server
server.listen(PORT, () => {
  console.log(`
🚀 WaaMan Server is running!
📍 Server: http://localhost:${PORT}
🌐 Environment: ${process.env.NODE_ENV || "development"}
📊 Real-time updates: Enabled
🔐 JWT Authentication: Enabled
  `)
})

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully")
  server.close(() => {
    console.log("Process terminated")
  })
})
