// API Client for WaaMan Application
class WaaManAPI {
  constructor() {
    this.baseURL = window.location.origin
    this.token = localStorage.getItem("waamanToken")
    this.socket = null
  }

  // Initialize Socket.IO connection
  initSocket() {
    const io = window.io // Declare the io variable before using it
    if (typeof io !== "undefined") {
      this.socket = io()

      this.socket.on("connect", () => {
        console.log("Connected to server")

        // Join appropriate room based on user role
        const user = JSON.parse(localStorage.getItem("waamanUser") || "{}")
        if (user.role) {
          this.socket.emit("join", user)
        }
      })

      // Listen for real-time updates
      this.socket.on("vehicles:update", (vehicles) => {
        window.dispatchEvent(new CustomEvent("vehiclesUpdate", { detail: vehicles }))
      })

      this.socket.on("vehicle:location", (data) => {
        window.dispatchEvent(new CustomEvent("vehicleLocationUpdate", { detail: data }))
      })

      this.socket.on("complaint:new", (complaint) => {
        window.dispatchEvent(new CustomEvent("newComplaint", { detail: complaint }))
      })

      this.socket.on("complaint:update", (complaint) => {
        window.dispatchEvent(new CustomEvent("complaintUpdate", { detail: complaint }))
      })

      this.socket.on("emergency:sos", (alert) => {
        window.dispatchEvent(new CustomEvent("emergencyAlert", { detail: alert }))
      })

      this.socket.on("chat:message", (message) => {
        window.dispatchEvent(new CustomEvent("chatMessage", { detail: message }))
      })
    }
  }

  // Set authentication token
  setToken(token) {
    this.token = token
    localStorage.setItem("waamanToken", token)
  }

  // Get authentication headers
  getHeaders() {
    const headers = {
      "Content-Type": "application/json",
    }

    if (this.token) {
      headers["Authorization"] = `Bearer ${this.token}`
    }

    return headers
  }

  // Generic API request method
  async request(endpoint, options = {}) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        headers: this.getHeaders(),
        ...options,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "API request failed")
      }

      return data
    } catch (error) {
      console.error("API Error:", error)
      throw error
    }
  }

  // Authentication methods
  async login(email, password) {
    const data = await this.request("/api/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    })

    if (data.success) {
      this.setToken(data.token)
      localStorage.setItem("waamanUser", JSON.stringify(data.user))
    }

    return data
  }

  logout() {
    this.token = null
    localStorage.removeItem("waamanToken")
    localStorage.removeItem("waamanUser")

    if (this.socket) {
      this.socket.disconnect()
    }
  }

  // Vehicle methods
  async getVehicles() {
    return await this.request("/api/vehicles")
  }

  async getVehicle(id) {
    return await this.request(`/api/vehicles/${id}`)
  }

  async updateVehicleLocation(id, location) {
    return await this.request(`/api/vehicles/${id}/location`, {
      method: "PUT",
      body: JSON.stringify(location),
    })
  }

  async updateVehicleStatus(id, status) {
    return await this.request(`/api/vehicles/${id}/status`, {
      method: "PUT",
      body: JSON.stringify({ status }),
    })
  }

  // Complaint methods
  async getComplaints() {
    return await this.request("/api/complaints")
  }

  async createComplaint(complaint) {
    return await this.request("/api/complaints", {
      method: "POST",
      body: JSON.stringify(complaint),
    })
  }

  async updateComplaint(id, updates) {
    return await this.request(`/api/complaints/${id}`, {
      method: "PUT",
      body: JSON.stringify(updates),
    })
  }

  // Notification methods
  async getNotifications() {
    return await this.request("/api/notifications")
  }

  // Emergency methods
  async sendSOS(location, message) {
    return await this.request("/api/emergency/sos", {
      method: "POST",
      body: JSON.stringify({ location, message }),
    })
  }

  // Real-time methods
  updateLocation(vehicleId, location) {
    if (this.socket) {
      this.socket.emit("update:location", { vehicleId, location })
    }
  }

  sendChatMessage(message, room = "general") {
    if (this.socket) {
      this.socket.emit("chat:message", { message, room })
    }
  }
}

// Create global API instance
window.WaaManAPI = new WaaManAPI()

// Initialize socket connection when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  // Load Socket.IO from CDN
  const script = document.createElement("script")
  script.src = "/socket.io/socket.io.js"
  script.onload = () => {
    window.WaaManAPI.initSocket()
  }
  document.head.appendChild(script)
})
